import React from 'react';
import InputBase from '@mui/material/InputBase';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import SearchIcon from '@mui/icons-material/Search';
// eslint-disable-next-line import/no-extraneous-dependencies
import styled from '@mui/system/styled';

interface CustomSearchProps {
  // eslint-disable-next-line no-unused-vars
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  width?: string | number;
  placeholder?: string;
  className?: string;
  value?: string;
}

const CustomPaper = styled(Paper)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  borderRadius: '8px', // Set border-radius to 8px
  backgroundColor: '#F7F8FA',
  border: 'none',
  paddingLeft: theme.spacing(2),
}));

const CustomInput = styled(InputBase)(({ theme }) => ({
  marginLeft: theme.spacing(1),
  height: '44px',
  width: '100%',
  flex: 1,
}));

const CustomIconButton = styled(IconButton)(({ theme }) => ({
  padding: theme.spacing(1),
  color: '#8996A2',
}));

function ControlledSearch({
  onChange, placeholder = 'Search', width = 320, className, value,
}: CustomSearchProps) {
  return (
    <CustomPaper elevation={0}>

      <CustomInput
        onChange={onChange}
        placeholder={placeholder}
        value={value}
        inputProps={{
          'aria-label': 'search',
          style: {
            width,
          },
        }}
        className={className}
      />
      <CustomIconButton aria-label="search">
        <SearchIcon />
      </CustomIconButton>
    </CustomPaper>
  );
}

export default ControlledSearch;
