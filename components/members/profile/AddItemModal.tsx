/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable no-unused-vars */
import {
  Modal,
  TextField,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  Pagination,
} from '@mui/material';
import VaccinesOutlinedIcon from '@mui/icons-material/VaccinesOutlined';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import MoreHorizOutlinedIcon from '@mui/icons-material/MoreHorizOutlined';
import ControlledSearch from 'components/search/controlled-search';
import SelectBox from 'components/select-box/select-box';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import React, {
  useContext, useEffect, useState, useRef,
} from 'react';
import CheckIcon from '@mui/icons-material/Check';
import dayjs from 'dayjs';
import DateSelector from 'components/date-selector/date-selector';
import Close from '@mui/icons-material/Close';
import { ArrowBack, ArrowForward } from '@mui/icons-material';
import Search from 'components/search/search';
import API, { ORG_ID, TOKEN } from 'api/src/lib/api';
import { NETWORKS } from 'api/src/routes/networks';
import CustomTable from 'components/table-components/table';
import { Network } from 'types/networks-types';
import Cookies from 'js-cookie';
import { capitalizeWords } from 'utils/helper';

interface AddItemModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  handleAdd: (type: string, fields: any) => void;
  type: string;
  sections: Record<string, any[]>;
}

function AddItemModal({
  open,
  setOpen,
  handleAdd,
  type,
  sections,
}: AddItemModalProps) {
  const constant = useContext(ConstantsContext);
  const backdropRef = useRef<HTMLDivElement | null>(null);
  const mouseDownOnBackdrop = useRef(false);

  const sectionInputs = {
    contacts: [
      { label: 'Name', name: 'name', type: 'text' },
      {
        label: 'Contact Type',
        name: 'role',
        type: 'select',
        options: constant?.associatedPersonRole || [],
      },
      { label: 'Relationship', name: 'relationship', type: 'text' },
      { label: 'Phone', name: 'phone', type: 'text' },
      {
        label: 'Email (optional)',
        name: 'email',
        type: 'text',
        required: false,
      },
    ],
    problems: [
      { label: 'Title', name: 'title', type: 'text' },
      {
        label: 'Problem Type',
        name: 'type',
        type: 'select',
        options: constant?.problemTypes || [],
      },
      { label: 'Description', name: 'description', type: 'textarea' },
    ],

    diagnoses: [
      {
        label: 'Note (optional)',
        name: 'clinicalNote',
        type: 'textarea',
        required: false,
      },
      {
        label: 'Date Identified (optional)',
        name: 'dateIdentified',
        type: 'date',
        required: false,
      },
    ],

    medications: [
      {
        label: 'Type',
        name: 'type',
        type: 'select',
        options: [
          { key: 'medical', title: 'Medical' },
          { key: 'behavioral', title: 'Behavioral' },
        ],
      },
      {
        label: 'Note (optional)',
        name: 'clinicalNote',
        type: 'textarea',
        required: false,
      },
      {
        label: 'Date Identified (optional)',
        name: 'dateIdentified',
        type: 'date',
        required: false,
      },
    ],
  };

  const [step, setStep] = useState(1);
  const [prevStep, setPrevStep] = useState(1);
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const [showMissingModal, setShowMissingModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDiagnosis, setSelectedDiagnosis] = useState<any[]>([]);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [diagnosisList, setDiagnosisList] = useState<any[]>([]);
  const [diagnosisPage, setDiagnosisPage] = useState(1);
  const diagnosisPerPage = 8;
  const [diagnosisTotalItems, setDiagnosisTotalItems] = useState(0);
  const [medicationList, setMedicationList] = useState<any[]>([]);
  const [medicationPage, setMedicationPage] = useState(1);
  const medicationPerPage = 8;
  const [medicationTotalItems, setMedicationTotalItems] = useState(0);
  const [selectedMedication, setSelectedMedication] = useState<any[]>([]);
  const [medicationDetails, setMedicationDetails] = useState<any>(null);

  useEffect(() => {
    if (type && sections[type]) {
      const initialValues: Record<string, any> = {};
      sections[type].forEach((field: any) => {
        initialValues[field.name] = '';
      });
      setFormValues(initialValues);
    }
  }, [type]);

  function formatTitle(title: string) {
    const singular = title.endsWith('s') ? title.slice(0, -1) : title;
    if (singular.toLowerCase() === 'diagnose') return 'Diagnosis';
    return singular.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
  }

  const setFieldValue = (name: string, value: any) => {
    setFormValues((prev) => ({ ...prev, [name]: value }));
  };

  const fields = sectionInputs[type as keyof typeof sectionInputs] || [];
  const rowFields = fields.filter((f: any) => f.type !== 'textarea');
  const textAreas = fields.filter((f: any) => f.type === 'textarea');
  const multipleTextAreas = textAreas.length > 1;

  const validateFields = () => {
    const fieldDefs = sectionInputs[type as keyof typeof sectionInputs] || [];
    const missing: string[] = [];

    fieldDefs.forEach((field: any) => {
      const isRequired = field.required !== false; // default to true
      const value = formValues[field.name];

      const isEmpty = value === null
        || value === undefined
        || (typeof value === 'string' && value.trim() === '')
        || (typeof value === 'object' && Object.keys(value).length === 0);

      if (isRequired && isEmpty) {
        missing.push(field.label);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setShowMissingModal(true);
      return false;
    }

    return true;
  };

  function renderInput(field: any) {
    const value = formValues[field.name] ?? '';

    switch (field.type) {
      case 'text':
        return (
          <TextField
            key={field.name}
            name={field.name}
            placeholder={field.label}
            label={field.label}
            value={value}
            variant="filled"
            onChange={(e) => setFieldValue(field.name, e.target.value)}
            fullWidth
            className="mt-0"
          />
        );
      case 'select':
        return (
          <SelectBox
            key={field.name}
            label={field.label}
            keyVal={field.name}
            defaultValue={value?.key || ''}
            onChange={(e) => {
              const selectedKey = e.target.value;
              setFieldValue(field.name, {
                key: selectedKey,
                title:
                  field.options.find(
                    (o: any) => o.key.toLowerCase() === selectedKey.toLowerCase(),
                  )?.title || '',
              });
            }}
            items={field.options || []}
          />
        );
      case 'date':
        return (
          <DateSelector
            key={field.name}
            name={field.name}
            defaultValue={value}
            pickerProps={{
              label: field.label,
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => setFieldValue(field.name, ''),
                },
              },
            }}
            onChange={(e) => {
              const date = dayjs(e.target.value).format('YYYY-MM-DD');
              setFieldValue(field.name, date);
            }}
          />
        );
      case 'datetime':
        return (
          <DateSelector
            key={field.name}
            name={field.name}
            defaultValue={value}
            time
            dateTimePickerProps={{
              label: field.label,
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => setFieldValue(field.name, ''),
                },
              },
            }}
            onChange={(e) => {
              const date = dayjs(e.target.value).format('YYYY-MM-DD HH:mm');
              setFieldValue(field.name, date);
            }}
          />
        );
      case 'textarea':
        return (
          <TextField
            key={field.name}
            name={field.name}
            label={field.label}
            value={value}
            variant="filled"
            multiline
            rows={multipleTextAreas ? 3 : 6}
            onChange={(e) => setFieldValue(field.name, e.target.value)}
            fullWidth
            sx={{ backgroundColor: '#F7F8FA', marginTop: 2 }}
          />
        );
      default:
        return null;
    }
  }

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearch = e.target.value;
    setSearchTerm(newSearch);
    setSearch(newSearch);
  };

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300); // 300ms delay

    return () => clearTimeout(handler); // clear previous timer
  }, [search]);

  useEffect(() => {
    if (debouncedSearch && type === 'medications') {
      const fetchMedication = async () => {
        const response: any = await API.CAREPLANS.searchMedication(
          debouncedSearch,
          TOKEN(),
        );

        // Filter out items with displayName === 'Unknown'
        const filtered = response.medications.filter(
          (med: any) => med.displayName !== 'Unknown',
        );

        // Remove duplicates based on rxcui
        const uniqueByRxcui = Array.from(
          new Map(filtered.map((med: any) => [med.rxcui, med])).values(),
        );

        setMedicationList(uniqueByRxcui);
        setMedicationTotalItems(uniqueByRxcui.length);
      };
      fetchMedication();
    } else if (debouncedSearch && type === 'diagnoses') {
      const fetchDiagnosis = async () => {
        const response: any = await API.CAREPLANS.searchWHODiagnosis(
          debouncedSearch,
          TOKEN(),
        );
        setDiagnosisList(response.results);
        setDiagnosisTotalItems(response.totalResults);
      };
      fetchDiagnosis();
    }
  }, [debouncedSearch, type]);

  const pagedDiagnosisList = diagnosisList?.slice(
    (diagnosisPage - 1) * diagnosisPerPage,
    diagnosisPage * diagnosisPerPage,
  );
  const pagedMedicationList = medicationList?.slice(
    (medicationPage - 1) * medicationPerPage,
    medicationPage * medicationPerPage,
  );

  const fetchMedicationDetails = async (rxcui: string) => {
    const response: any = await API.CAREPLANS.fetchMedicationDetails(
      rxcui,
      TOKEN(),
    );
    setMedicationDetails(response.medication);
  };

  const createHandleAddModel = () => {
    if (type === 'medications') {
      return {
        ...formValues,
        name: medicationDetails.displayName,
        rxcui: medicationDetails.rxcui,
        description: formValues.clinicalNote || '',
        icdCode: medicationDetails.rxcui,
        type: formValues.type,
        source: 'care team',
      };
    }
    if (type === 'diagnoses') {
      return {
        ...formValues,
        title: selectedDiagnosis[0].title,
        description: selectedDiagnosis[0].id,
        icdCode: selectedDiagnosis[0].theCode,
        source: 'care team',
      };
    }
    return {
      ...formValues,
      source: 'care team',
    };
  };

  return (
    <>
      <Modal open={open} onClose={() => setOpen(false)}>
        <div
          ref={backdropRef}
          className="fixed inset-0 flex items-center justify-center p-4 z-50"
          onMouseDown={(e) => {
            mouseDownOnBackdrop.current = e.target === e.currentTarget;
          }}
          onMouseUp={(e) => {
            if (mouseDownOnBackdrop.current && e.target === e.currentTarget) {
              setOpen(false);
            }
          }}
        >
          <div
            className="flex flex-col h-[95vh] w-[90%] bg-white rounded-lg overflow-hidden"
            // onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                if (!validateFields()) return;
                handleAdd(type, formValues);
                setFormValues({});
                setMissingFields([]);
                setOpen(false);
              }
            }}
            tabIndex={0}
          >
            {/* Header */}
            <div
              style={{ borderBottom: '1px solid #E0E0E0' }}
              className="h-[64px] shrink-0 items-center relative justify-center grid grid-flow-col border border-black"
            >
              <div className="grid grid-flow-row p-4 items-center text-center">
                <p className="m-0 font-[500] text-[18px]">
                  Add
                  {' '}
                  {formatTitle(type)}
                </p>
              </div>
              <Close
                className="absolute right-5 cursor-pointer"
                onClick={() => setOpen(false)}
              />
            </div>

            {/* Body */}
            <div className="relative w-full flex flex-col flex-grow overflow-hidden overflow-y-auto min-h-0 px-[40px] pt-4 pb-5">
              {(type === 'diagnoses' || type === 'medications') && (
                <p className="text-[15px] font-[500] text-[#747A7A] mt-1 mb-2">
                  Step
                  {' '}
                  {step}
                  {' '}
                  of
                  {' '}
                  {type === 'medications' && 3}
                  {type === 'diagnoses' && 2}
                </p>
              )}

              <div
                className="relative w-full overflow-hidden flex-grow flex flex-col"
                style={{ minHeight: '300px' }}
              >
                {/* Step 1 – Select Diagnosis or Medication */}
                {type !== 'problems' && type !== 'contacts' && (
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease flex flex-col"
                    style={{
                      transform:
                        step === 1 ? 'translateX(0%)' : 'translateX(-3%)',
                      opacity: step === 1 ? 1 : 0,
                      zIndex: step === 1 ? 2 : 1,
                    }}
                  >
                    <p className="m-0 p-0 font-[600] text-lg">
                      {type === 'medications'
                        ? 'Select Medication'
                        : 'Select Diagnosis'}
                    </p>
                    <div className="flex gap-x-5 gap-y-3 mt-[10px] flex-wrap">
                      <div className="w-[400px] ml-[1px]">
                        <ControlledSearch
                          placeholder={`Search ${
                            type === 'medications' ? 'medication' : 'diagnoses'
                          }...`}
                          onChange={handleSearch}
                          className="h-[56px]"
                          value={searchTerm}
                        />
                      </div>
                    </div>
                    {type === 'diagnoses' && (
                      <div className="mt-4 overflow-y-auto flex-grow pb-[0px]">
                        {diagnosisList?.length > 0 ? (
                          <CustomTable
                            source={pagedDiagnosisList}
                            variant="select-diagnosis-table"
                            metadata={{
                              list: diagnosisList,
                              setSelected: setSelectedDiagnosis,
                              disableMultiSelect: true,
                            }}
                            style={{
                              border: 'solid 1px lightgray',
                              borderRadius: '10px',
                              borderBottom: 'none',
                            }}
                            headCellStyle={{
                              padding: '4px 16px',
                              fontSize: '13px',
                              color: '#747A7A',
                            }}
                            tableCellStyle={{
                              padding: '0px 16px',
                              fontSize: '15px',
                            }}
                            selected={selectedDiagnosis || []}
                            setSelected={setSelectedDiagnosis}
                            select
                            pageInfo={{
                              page: diagnosisPage,
                              per: diagnosisPerPage,
                              total: diagnosisTotalItems,
                            }}
                            onPageChange={(newPage) => {
                              setDiagnosisPage(newPage);
                            }}
                          />
                        ) : (
                          <div className="flex h-full">
                            <p className="text-[15px] font-[400] text-[#747A7A] italic ml-6">
                              Search for a diagnosis to select.
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                    {type === 'medications' && (
                      <div className="mt-4 overflow-y-auto flex-grow pb-[0px]">
                        {medicationTotalItems > 0 ? (
                          <CustomTable
                            source={pagedMedicationList}
                            variant="select-medication-table"
                            metadata={{
                              list: medicationList,
                              setSelected: setSelectedMedication,
                              disableMultiSelect: true,
                            }}
                            style={{
                              border: 'solid 1px lightgray',
                              borderRadius: '10px',
                              borderBottom: 'none',
                            }}
                            headCellStyle={{
                              padding: '4px 16px',
                              fontSize: '13px',
                              color: '#747A7A',
                            }}
                            tableCellStyle={{
                              padding: '0px 16px',
                              fontSize: '15px',
                            }}
                            selected={selectedMedication || []}
                            setSelected={setSelectedMedication}
                            select
                            pageInfo={{
                              page: medicationPage,
                              per: medicationPerPage,
                              total: medicationTotalItems,
                            }}
                            onPageChange={(newPage) => {
                              setMedicationPage(newPage);
                            }}
                            rowIdField="rxcui"
                          />
                        ) : (
                          <div className="flex h-full">
                            <p className="text-[15px] font-[400] text-[#747A7A] italic ml-6">
                              Search for a medication to select.
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {(type === 'problems' || type === 'contacts') && (
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease flex flex-col"
                    style={{
                      transform:
                        step === 1 ? 'translateX(0%)' : 'translateX(-3%)',
                      opacity: step === 1 ? 1 : 0,
                      zIndex: step === 1 ? 2 : 1,
                    }}
                  >
                    <div className="pt-4 overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {rowFields.map((field: any) => (
                          <div key={field.name}>{renderInput(field)}</div>
                        ))}
                      </div>
                      {textAreas.map((field: any) => (
                        <div
                          key={field.name}
                          className={
                            textAreas.length === 1
                              ? 'flex-grow flex flex-col'
                              : ''
                          }
                        >
                          {renderInput(field)}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Step 2 – Additional Info */}
                <div
                  className="absolute w-full h-full transition-all duration-200 ease"
                  style={{
                    transform:
                      step === 2
                        ? 'translateX(0%)'
                        : step === 3
                          ? 'translateX(-3%)'
                          : 'translateX(3%)',
                    opacity: step === 2 ? 1 : 0,
                    zIndex: step === 2 ? 2 : 1,
                  }}
                >
                  {type === 'medications' && medicationDetails && (
                    <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                      <p className="m-0 p-0 font-[600] text-lg mb-6">
                        Medication Details
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-8">
                        <div className="flex flex-col">
                          <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                            Name
                          </p>
                          <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                            {capitalizeWords(
                              medicationDetails.displayName,
                              true,
                            ) || '-'}
                          </p>
                        </div>
                        <div className="flex flex-col">
                          <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                            Generic
                          </p>
                          <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                            {medicationDetails.isGeneric ? 'Yes' : 'No'}
                          </p>
                        </div>
                        <div className="flex flex-col">
                          <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                            Prescribable
                          </p>
                          <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                            {medicationDetails.isPrescribable ? 'Yes' : 'No'}
                          </p>
                        </div>
                        <div className="flex flex-col">
                          <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                            Route
                          </p>
                          <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                            {medicationDetails.route
                              || medicationDetails.doseForm
                              || '-'}
                          </p>
                        </div>
                        <div className="flex flex-col">
                          <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                            Rx #
                          </p>
                          <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                            {medicationDetails.rxcui || '-'}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                  {type !== 'medications' && (
                    <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                      <p className="m-0 p-0 font-[600] text-lg">
                        Additional Information
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-[10px]">
                        {rowFields.map((field: any) => (
                          <div
                            key={field.name}
                            className="flex flex-col h-[56px]"
                          >
                            {renderInput(field)}
                          </div>
                        ))}
                      </div>
                      {textAreas.map((field: any) => (
                        <div
                          key={field.name}
                          className={
                            textAreas.length === 1
                              ? 'flex-grow flex flex-col'
                              : ''
                          }
                        >
                          {renderInput(field)}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Step 3 – Additional Info */}
                <div
                  className="absolute w-full h-full transition-all duration-200 ease"
                  style={{
                    transform: step === 3 ? 'translateX(0%)' : 'translateX(3%)',
                    opacity: step === 3 ? 1 : 0,
                    zIndex: step === 3 ? 2 : 1,
                  }}
                >
                  <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                    <p className="m-0 p-0 font-[600] text-lg">
                      Additional Information
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-[10px] ml-[1px]">
                      {rowFields.map((field: any) => (
                        <div
                          key={field.name}
                          className="flex flex-col h-[56px]"
                        >
                          {renderInput(field)}
                        </div>
                      ))}
                    </div>
                    {textAreas.map((field: any) => (
                      <div
                        key={field.name}
                        className={
                          textAreas.length === 1
                            ? 'flex-grow flex flex-col'
                            : ''
                        }
                      >
                        {renderInput(field)}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div
              id="footer-area"
              style={{
                borderTop: '1px solid #E0E0E0',
                zIndex: 2,
                display: step === 1 && !type ? 'none' : 'flex',
              }}
              className="shrink-0 bg-white border-t border-gray-300 flex items-center justify-between p-3"
            >
              {step > 1 ? (
                <Button
                  className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px] text-[#262D2D] font-[500] bg-gradient-to-b from-[#F7F8F8] to-[#DCDFDF] hover:bg-gradient-to-b hover:from-[#F9FAFA] hover:to-[#D0D3D3]"
                  onClick={() => setStep(step - 1)}
                >
                  <ArrowBack className="mr-3 w-[18px] h-[18px]" />
                  <p className="font-[500] m-0 p-0 pt-[1px]">Previous</p>
                </Button>
              ) : (
                <div />
              )}
              <Button
                variant="contained"
                className={`bg-[#2D62ED] text-white rounded-md !min-w-fit ${
                  step === 1
                  && ((type === 'medications' && !selectedMedication[0]?.rxcui)
                    || (type === 'diagnoses' && !selectedDiagnosis[0]))
                  && 'opacity-50'
                }`}
                disabled={
                  (step === 1
                    && type === 'medications'
                    && !selectedMedication[0]?.rxcui)
                  || (type === 'diagnoses' && !selectedDiagnosis[0])
                }
                onClick={() => {
                  if (step === 1 || (step === 2 && type === 'medications')) {
                    if (type === 'problems') {
                      if (!validateFields()) return;
                      const formData = {
                        ...formValues,
                        title: formValues.title,
                        description: formValues.description,
                        type: formValues.type,
                        source: 'care team',
                      };
                      handleAdd(type, formData);
                      setFormValues({});
                      setMissingFields([]);
                      setOpen(false);
                    }
                    if (type === 'contacts') {
                      if (!validateFields()) return;
                      handleAdd(type, formValues);
                      setFormValues({});
                      setMissingFields([]);
                      setOpen(false);
                    }
                    if (step === 1 && type === 'medications') {
                      fetchMedicationDetails(selectedMedication[0].rxcui);
                    }
                    setStep(step + 1);
                  } else {
                    if (!validateFields()) return;
                    const handleAddData = createHandleAddModel();
                    handleAdd(type, handleAddData);
                    setFormValues({});
                    setMissingFields([]);
                    setOpen(false);
                  }
                }}
              >
                <div className="grid grid-flow-col items-center gap-2">
                  {((step === 2 && type !== 'medications')
                    || (step === 1 && (type === 'problems' || type === 'contacts'))
                    || (step === 3 && type === 'medications')) && <CheckIcon />}
                  {step < 2
                    ? step === 1 && (type === 'problems' || type === 'contacts')
                      ? 'Save'
                      : 'Next'
                    : step === 2 && type === 'medications'
                      ? 'Next'
                      : 'Save'}
                  {step < 2 ? (
                    step === 1 && (type === 'problems' || type === 'contacts') ? (
                      <div />
                    ) : (
                      <ArrowForward />
                    )
                  ) : (
                    <div />
                  )}
                  {step === 2 && type === 'medications' && <ArrowForward />}
                </div>
              </Button>
            </div>
          </div>

          {/* Gradient Definitions */}
          <svg width="0" height="0">
            <defs>
              <linearGradient
                id="radioGradient"
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <stop
                  offset="0%"
                  style={{ stopColor: '#0198A5', stopOpacity: 1 }}
                />
                <stop
                  offset="100%"
                  style={{ stopColor: '#008390', stopOpacity: 1 }}
                />
              </linearGradient>
            </defs>
          </svg>
        </div>
      </Modal>
      <Modal open={showMissingModal} onClose={() => setShowMissingModal(false)}>
        <div
          className="fixed inset-0 flex items-center justify-center p-4 z-50"
          onClick={() => setShowMissingModal(false)}
        >
          <div
            className="bg-white max-w-md w-full p-6 rounded shadow-md"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-lg font-semibold mb-2 text-center">
              Please complete all required fields:
            </h2>
            <ul className="list-disc list-inside text-sm text-red-600 mb-4">
              {missingFields.map((label) => (
                <li key={label}>{label}</li>
              ))}
            </ul>
            <div className="flex justify-center">
              <Button
                onClick={() => setShowMissingModal(false)}
                variant="contained"
                className="rounded-md"
              >
                OK
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default AddItemModal;
