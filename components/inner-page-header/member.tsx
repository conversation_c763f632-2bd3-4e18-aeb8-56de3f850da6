/* eslint-disable jsx-a11y/no-static-element-interactions */

import { useLoading } from 'contexts/loading-context/loading-context';
import { useRouter } from 'next/router';
import React, { useCallback } from 'react';
import { capitalizeWords } from 'utils/helper';
import useAuth from 'hooks/use-auth';
import TabsView from 'components/tabs-view';

interface InnerPageHeaderProps {
  add?: boolean;
  title: string;
  addLabel?: string;
  updateLabel: string;
  btnName?: string;
  backRoute?: string;
  // eslint-disable-next-line no-undef
  rightSide?: React.ReactNode;
  callback?: () => void;
  active?: string;
  memberId?: string;
  fullName?: string;
  updateUrl?: string;
  replaceTabs?: any;
  replaceTab?: any;
  replaceTabClick?: (tab: any) => void;
  add2?: boolean;
  addLabel2?: string;
  borderBottom?: boolean;
}

/* eslint-disable jsx-a11y/click-events-have-key-events */
function MemberInnerPageHeader({
  title,
  add,
  add2,
  updateLabel,
  addLabel,
  addLabel2,
  backRoute,
  btnName,
  rightSide = null,
  callback = () => null,
  active,
  memberId,
  updateUrl,
  fullName = 'No Name',
  replaceTabs,
  replaceTab,
  replaceTabClick,
  borderBottom = false,
}: InnerPageHeaderProps) {
  const router = useRouter();

  const auth = useAuth();
  const orgIncludesSocialPlans = auth.user?.org?.meta?.features.includes('socialPlans');

  const checkActiveTab = (tabValue: string) => tabValue === active;
  const tabs = [
    {
      label: 'Profile',
      value: 'profile',
      url: '/members/profile',
    },
    {
      label: 'Household',
      value: 'household',
      url: '/members/households',
    },
    {
      label: 'Insurance',
      value: 'insurance',
      url: '/members/insurance',
    },
    {
      label: 'Timeline',
      value: 'timeline',
      url: '/members/timeline',
    },
    {
      label: 'Encounters',
      value: 'encounters',
      url: '/members/encounters',
    },
    ...(orgIncludesSocialPlans
      ? [
        {
          label: 'Social Plans',
          value: 'social-plans',
          url: '/members/social-plans',
        },
      ]
      : []),
    {
      label: 'Care Plans',
      value: 'care-plans',
      url: '/members/care-plans',
    },
    {
      label: 'Attachments',
      value: 'attachments',
      url: '/members/attachments',
    },
    {
      label: 'Notes',
      value: 'notes',
      url: '/members/notes',
    },
    // {
    //   label: 'Chats',
    //   value: 'chats',
    //   url: '/members/chats',
    // },
    {
      label: 'Tasks',
      value: 'tasks',
      url: '/members/tasks',
    },
  ];

  const { hideLoading, showLoading } = useLoading();
  const handleClick = useCallback(async (route: any) => {
    showLoading();
    await router.push(route);
    hideLoading();
  }, []);

  function handleCrumbClick() {
    if (updateUrl) {
      router.push(updateUrl);
    }
  }
  return (
    <div className={`flex flex-col w-full ${borderBottom ? 'border-bottom' : ''}`}>
      <div
        className={`flex items-center justify-between w-full p-5 flex-wrap gap-y-2 ${
          rightSide ? '' : 'border-bottom'
        }`}
      >
        <p className="text-[21px] p-0 m-0 font-[600] col-span-3 mr-4">
          <span
            className="font-[300] cursor-pointer"
            onClick={() => {
              router.push('/members');
            }}
          >
            {title}
          </span>
          <span
            className="font-[300] cursor-pointer"
            onClick={() => {
              if (fullName === 'Add Member') {
                callback();
              } else {
                router.push(`/members/profile/${memberId}`);
              }
            }}
          >
            {` > ${capitalizeWords(fullName, true)}`}
          </span>
          {/* {` > ${add ? addLabel : updateLabel}`} */}
          <span className="font-[300]">{' > '}</span>
          <span
            onClick={handleCrumbClick}
            className={updateUrl && 'font-[300] cursor-pointer'}
          >
            {updateLabel}
          </span>
          {add ? (
            <>
              <span className="font-[300]">{' > '}</span>
              <span>{addLabel}</span>
            </>
          ) : (
            ''
          )}
          {add2 ? (
            <>
              <span className="font-[300]">{' > '}</span>
              <span>{addLabel2}</span>
            </>
          ) : (
            ''
          )}
        </p>
        {rightSide && rightSide}
      </div>
      {!rightSide && (
        <div className="border-bottom py-1 flex items-center">
          {replaceTabs && (
            <TabsView
              tabs={replaceTabs}
              className="grid grid-flow-col auto-cols-max gap-0 ml-5"
              value={replaceTab}
              callback={replaceTabClick || (() => {})}
            />
          )}
          <div className="pl-5 py-3 flex items-center">
            {!replaceTabs
              && tabs.map((tab: any, index: number) => (
                <div
                  key={tab.value}
                  onClick={() => handleClick(`${tab?.url as string}/${memberId}`)}
                  style={{
                    background: !checkActiveTab(tab.value)
                      ? 'linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%)'
                      : 'linear-gradient(180deg, #404848 0%, #868B8B 100%)',
                  }}
                  // eslint-disable-next-line no-nested-ternary
                  className={`cursor-pointer w-[144px] h-[38px] grid  content-center ${
                    // eslint-disable-next-line no-nested-ternary
                    index === 0
                      ? 'rounded-l-lg'
                      : index === tabs.length - 1
                        ? 'rounded-r-lg'
                        : ''
                  } ${
                    index !== 0 && index !== tabs.length - 1
                      ? 'border-l-0 border-r-0'
                      : ''
                  }`}
                >
                  <p
                    className={`text-center font-[500] text-sm ${
                      checkActiveTab(tab.value)
                        ? 'text-white'
                        : 'text-[#262D2D]'
                    }`}
                  >
                    {tab.label}
                  </p>
                </div>
              ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default MemberInnerPageHeader;
