import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import NotificationBell from './notification-bell';
import useNotifications from 'hooks/use-notifications';

// Mock the useNotifications hook
jest.mock('hooks/use-notifications');
const mockUseNotifications = useNotifications as jest.MockedFunction<typeof useNotifications>;

// Mock dayjs
jest.mock('dayjs', () => {
  const originalDayjs = jest.requireActual('dayjs');
  const mockDayjs = (date?: any) => ({
    ...originalDayjs(date),
    fromNow: () => '2 minutes ago',
  });
  mockDayjs.extend = jest.fn();
  return mockDayjs;
});

const mockNotifications = [
  {
    id: '1',
    kind: 'task',
    title: 'New Task Assigned',
    message: 'You have been assigned a new task',
    read: false,
    userID: 'user1',
    createdAt: '2023-12-01T10:00:00Z',
    updatedAt: '2023-12-01T10:00:00Z',
    meta: { data: { task: 'task-123' } },
  },
  {
    id: '2',
    kind: 'notification',
    title: 'System Update',
    message: 'System will be updated tonight',
    read: true,
    userID: 'user1',
    createdAt: '2023-12-01T09:00:00Z',
    updatedAt: '2023-12-01T09:00:00Z',
    meta: { data: {} },
  },
];

const defaultMockReturn = {
  notifications: mockNotifications,
  unreadCount: 1,
  totalCount: 2,
  currentPage: 1,
  totalPages: 1,
  loading: false,
  error: null,
  fetchNotifications: jest.fn(),
  markAsRead: jest.fn(),
  markAllAsRead: jest.fn(),
  deleteNotifications: jest.fn(),
  refreshNotifications: jest.fn(),
};

describe('NotificationBell', () => {
  beforeEach(() => {
    mockUseNotifications.mockReturnValue(defaultMockReturn);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders notification bell with badge', () => {
    render(<NotificationBell />);
    
    const badge = screen.getByText('1');
    expect(badge).toBeInTheDocument();
  });

  it('opens popover when bell is clicked', async () => {
    render(<NotificationBell />);
    
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);
    
    await waitFor(() => {
      expect(screen.getByText('Notifications')).toBeInTheDocument();
    });
  });

  it('displays notifications in the list', async () => {
    render(<NotificationBell />);
    
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);
    
    await waitFor(() => {
      expect(screen.getByText('New Task Assigned')).toBeInTheDocument();
      expect(screen.getByText('System Update')).toBeInTheDocument();
    });
  });

  it('shows empty state when no notifications', async () => {
    mockUseNotifications.mockReturnValue({
      ...defaultMockReturn,
      notifications: [],
      unreadCount: 0,
    });

    render(<NotificationBell />);
    
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);
    
    await waitFor(() => {
      expect(screen.getByText('No unread notifications')).toBeInTheDocument();
    });
  });

  it('calls fetchNotifications when opened', async () => {
    const mockFetchNotifications = jest.fn();
    mockUseNotifications.mockReturnValue({
      ...defaultMockReturn,
      fetchNotifications: mockFetchNotifications,
    });

    render(<NotificationBell />);
    
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);
    
    expect(mockFetchNotifications).toHaveBeenCalledWith(1, true);
  });

  it('shows loading state', async () => {
    mockUseNotifications.mockReturnValue({
      ...defaultMockReturn,
      loading: true,
    });

    render(<NotificationBell />);
    
    const bellButton = screen.getByRole('button', { name: /notifications/i });
    fireEvent.click(bellButton);
    
    await waitFor(() => {
      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });
});
