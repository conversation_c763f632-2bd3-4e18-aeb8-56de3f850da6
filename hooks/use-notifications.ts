import { useState, useCallback } from 'react';
import API from 'api/src/lib/api';
import {
  Notification,
  NotificationFilters,
  MarkNotificationsReadRequest,
  DeleteNotificationsRequest
} from 'types/notification-types';
import { useLoading } from 'contexts/loading-context/loading-context';
import { useModal } from 'contexts/modal-context/modal-context';
import useAuth from './use-auth';

interface UseNotificationsReturn {
  notifications: Notification[];
  unreadCount: number;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: (page?: number, read?: boolean) => Promise<void>;
  fetchNotificationCount: () => Promise<void>;
  markAsRead: (ids: string[]) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotifications: (ids: string[]) => Promise<void>;
  refreshNotifications: () => Promise<void>;
}

const useNotifications = (): UseNotificationsReturn => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useAuth();
  const { showModal } = useModal();
  const { showLoading, hideLoading } = useLoading();

  const fetchNotifications = useCallback(async (page = 1, read?: boolean) => {
    if (!user?.id) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const filters: NotificationFilters = {
        userID: user.id,
        page,
        per: 10,
        ...(read !== undefined && { read }),
      };
      
      const data: any = await API.NOTIFICATIONS.getNotifications(filters);

      setNotifications(data.items);
      setTotalCount(data.metadata.total);
      setCurrentPage(data.metadata.page);
      setTotalPages(Math.ceil(data.metadata.total / data.metadata.per));

      // Calculate unread count
      const unread = data.items.filter((notification: any) => !notification.read).length;
      setUnreadCount(unread);

    } catch (err: any) {
      const errorMessage = err?.reason || err?.message || 'Failed to fetch notifications';
      setError(errorMessage);
      showModal('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  }, [user?.id, showModal]);

  const fetchNotificationCount = useCallback(async () => {
    if (!user?.id) return;

    try {
      const data: any = await API.NOTIFICATIONS.getNotificationCount(user.id, false);
      setUnreadCount(data.unread || 0);
      setTotalCount(data.total || 0);
    } catch (err: any) {
      // Silently fail for count requests to avoid disrupting UX
      console.warn('Failed to fetch notification count:', err?.reason || err?.message);
    }
  }, [user?.id]);

  const markAsRead = useCallback(async (ids: string[]) => {
    if (!ids.length) return;
    
    showLoading();
    try {
      const request: MarkNotificationsReadRequest = {
        ids,
        state: true,
      };
      
      await API.NOTIFICATIONS.markNotificationsAsRead(request);
      
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          ids.includes(notification.id) 
            ? { ...notification, read: true }
            : notification
        )
      );
      
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - ids.length));

      // Refresh count from server to ensure accuracy
      await fetchNotificationCount();

    } catch (err: any) {
      const errorMessage = err?.data?.reason || 'Failed to mark notifications as read';
      showModal('Error', errorMessage);
    } finally {
      hideLoading();
    }
  }, [showLoading, hideLoading, showModal, fetchNotificationCount]);

  const markAllAsRead = useCallback(async () => {
    const unreadIds = notifications
      .filter(notification => !notification.read)
      .map(notification => notification.id);
    
    if (unreadIds.length > 0) {
      await markAsRead(unreadIds);
    }
  }, [notifications, markAsRead]);

  const deleteNotifications = useCallback(async (ids: string[]) => {
    if (!ids.length) return;
    
    showLoading();
    try {
      const request: DeleteNotificationsRequest = { ids };
      
      await API.NOTIFICATIONS.deleteNotifications(request);
      
      // Update local state
      setNotifications(prev => 
        prev.filter(notification => !ids.includes(notification.id))
      );
      
      // Update counts
      const deletedUnreadCount = notifications
        .filter(notification => ids.includes(notification.id) && !notification.read)
        .length;
      
      setUnreadCount(prev => Math.max(0, prev - deletedUnreadCount));
      setTotalCount(prev => Math.max(0, prev - ids.length));

      // Refresh count from server to ensure accuracy
      await fetchNotificationCount();

    } catch (err: any) {
      const errorMessage = err?.reason || err?.message || 'Failed to delete notifications';
      showModal('Error', errorMessage);
    } finally {
      hideLoading();
    }
  }, [notifications, showLoading, hideLoading, showModal, fetchNotificationCount]);

  const refreshNotifications = useCallback(async () => {
    await fetchNotifications(currentPage);
  }, [fetchNotifications, currentPage]);

  return {
    notifications,
    unreadCount,
    totalCount,
    currentPage,
    totalPages,
    loading,
    error,
    fetchNotifications,
    fetchNotificationCount,
    markAsRead,
    markAllAsRead,
    deleteNotifications,
    refreshNotifications,
  };
};

export default useNotifications;
