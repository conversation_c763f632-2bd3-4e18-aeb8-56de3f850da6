/* eslint-disable default-param-last */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, ORG_ID, TOKEN, instance, rawInstance,
} from 'api/src/lib/api';
import {
  MemberAttachment,
  MemberCreate,
  StudentCreate,
  MemberAddress,
} from 'api/src/types';
import { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/no-cycle

export const MEMBERS = {
  fetchMembers: async (
    token?: string,
    orgId?: string,
    per = 10,
    page = 1,
    sortBy = 'firstName',
    sortDirection = 'desc',
    search = '',
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/all?org=${
        ORG_ID() || orgId
      }&per=${per}&page=${page}&sortBy=${sortBy}&sortDirection=${sortDirection}&search=${search}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  fetchMembersByTeam: async (
    teamId: string,
    token?: string,
    orgId?: string,
    per = 5,
    page = 1,
    sortBy = 'firstName',
    sortDirection = 'desc',
    search = '',
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/team?team=${teamId}&org=${
        ORG_ID() || orgId
      }&per=${per}&page=${page}&sortBy=${sortBy}&sortDirection=${sortDirection}&search=${search}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMembersEncounters: async (
    memberID: string,
    status: string,
    page: number,
    per: number,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/surveys`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      params: {
        memberID,
        status,
        page,
        per,
      },
    };
    return instance(config);
  },

  fetchMemberAttachments: async (
    memberID: string,
    kind: string,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/attachments?kind=${kind}&page=0&per=100`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      params: {
        kind,
      },
    };
    return instance(config);
  },

  fetchMemberNotes: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/notes?per=1000&page=0`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  fetchMemberTasks: async (
    memberId: string,
    orgId?: string,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/tasks/filter`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        orgID: orgId,
        receivers: [memberId],
      },
    };
    return instance(config);
  },
  fetchMemberTimeLines: async (
    memberID: string,
    token?: string,
    params?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/timeline?memberId=${memberID}&${params}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMemberStatus: async (
    memberID: string,
    token?: string,
    params?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/timeline/status?memberId=${memberID}&${params}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMemberSocialPlans: async (
    memberID: string,
    token?: string,
    orgId?: string,
    per = 100,
    page = 1,
    sortBy = 'createdAt',
    sortDirection = 'desc',
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/carepackages?per=${per}&page=${page}&sortBy=${sortBy}&sortDirection=${sortDirection}&org=${
        ORG_ID() || orgId
      }`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMember: async (memberID: string, token?: string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMemberChats: async (memberID: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/chats`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  fetchMembersByHouseholds: async (households: string[]) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/all`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      params: {
        households,
      },
    };
    return instance(config);
  },

  fetchSurveyById: async (surveyId: string, token?: string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/surveys/${surveyId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchCheckSurveyById: async (
    surveyId: string,
    token?: string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/surveys/${surveyId}/questionAnswers`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  createSurvey: async (data: any, token?: string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/templates`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  fetchSurveyTypes: async (token?: string, orgId?: string, type?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/templates?org=${
        ORG_ID()?.toLowerCase() || orgId?.toLowerCase()
      }${type ? `&kind=${type}` : ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
    };
    return instance(config);
  },

  sendSurveyAnswers: async (data: any, token?: string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/answers?org=${ORG_ID() || orgId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  closeEncounter: async (surveyId: string, token?: string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/surveys/${surveyId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        status: 'complete',
      },
    };
    return instance(config);
  },

  fetchMemberEncounters: async (
    memberID: string,
    status?: string,
    page?: number,
    per?: number,
    token?: string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/surveys?memberID=${memberID}&status=${
        status || 'all'
      }&page=${page || 0}&per=${per || 100}&org=${ORG_ID() || orgId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      params: {
        memberID,
        status,
        page,
        per,
        orgId,
      },
    };
    return instance(config);
  },

  fetchMemberProfile: async (
    memberID: string,
    token?: string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/dashboard`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  createMemberAttachment: async (memberID: string, data: MemberAttachment) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/members/${memberID}/attachments`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },
  deleteMemberAttachment: async (attachmentID: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/attachments/${attachmentID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  createMember: async (data: MemberCreate) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/members`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  createStudent: async (data: StudentCreate) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/members`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  updateMember: async (memberID: string, model: any) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/members/${memberID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data: model,
    };
    return instance(config);
  },

  updateMemberAddress: async (memberID: string, address: MemberAddress) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/members/${memberID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: { address },
    };
    return instance(config);
  },

  deleteMember: async (memberID: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/members/${memberID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  deleteMemberEncounter: async (surveyID: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/surveys/${surveyID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  deleteMemberStatus: async (memberID: string, statusID: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/members/${memberID}/status/${statusID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  createMemberInsurance: async (data: any) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/insurances`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  getMemberInsurance: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/insurances`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  deleteMemberInsurance: async (insuranceID: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/insurances/${insuranceID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  getMemberInsuranceById: async (insuranceID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/insurances/${insuranceID}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  updateMemberInsurance: async (insuranceID: string, data: any) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/insurances/${insuranceID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  fetchCarriers: async (page: number, per: number, name: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/carriers?page=${page}&per=${per}&name=${name}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  deleteMemberTag: async (tagId: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/tags/${tagId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  fetchMemberConsents: async (
    memberID: string,
    token?: string,
    page = 1,
    per = 10,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/consents?page=${page}&per=${per}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMemberDiagnoses: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/members/${memberID}/diagnoses`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMemberMedications: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/members/${memberID}/medications`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchMemberProblems: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/members/${memberID}/problems`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  createMemberItemByType: async (memberID: string, type: string, data: any) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: type === 'problems' ? `${BASE_URL}/api/members/${memberID}/${type}/create` : `${BASE_URL}/api/members/${memberID}/${type}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data,
    };
    return rawInstance(config);
  },

  deleteMemberItemByType: async (memberID: string, type: string, id: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/api/members/${memberID}/${type}/${id}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return rawInstance(config);
  },

  fetchMemberAssociatedPersons: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/members/${memberID}/associated-persons`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchTeamsByMemberId: async (memberID: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${memberID}/teams`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  deleteContact: async (memberId: string, contactId: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/api/members/${memberId}/associated-persons/${contactId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return rawInstance(config);
  },

  addPrimaryNavigator: async (memberId: string, primaryNavigatorId: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/members/${memberId}/primaryUser`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: {
        userID: primaryNavigatorId,
      },
    };
    return rawInstance(config);
  },

  deletePrimaryNavigator: async (memberId: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/members/${memberId}/primaryUser`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return rawInstance(config);
  },

};
